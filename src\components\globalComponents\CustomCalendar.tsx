'use client';

import { useState } from 'react';
import { DateRange, type Range } from 'react-date-range';
import { format } from 'date-fns';
import 'react-date-range/dist/styles.css'; // core style
import 'react-date-range/dist/theme/default.css'; // default theme
import './custom-calendar.css'; // custom styles (you'll write this below)

import { Radio, RadioGroup } from '@heroui/react';

export default function CustomCalendar() {
  const [range, setRange] = useState<Range[]>([
    {
      startDate: new Date(), // today's date
      endDate: new Date(), // today's date
      key: 'selection',
    },
  ]);

  return (
    <div className="calendar-wrapper">
      <div className="calendar-header">
        <RadioGroup orientation="horizontal">
          <Radio value="buenos-aires">Calendar</Radio>
          <Radio value="sydney">Flexible Dates</Radio>
        </RadioGroup>
        <div className="flex flex-row gap-3">
          <div className="calendar-range-label">
            {format(range[0].startDate!, 'MMM d')} -{' '}
            {format(range[0].endDate!, 'MMM d')}
          </div>
          <div className="calendar-confirm">✔️</div>
        </div>
      </div>

      <DateRange
        editableDateInputs
        onChange={item => setRange([item.selection])}
        moveRangeOnFirstSelection={false}
        months={2}
        direction="horizontal"
        rangeColors={['#E9E7FB']}
        ranges={range}
        showDateDisplay={false}
        minDate={new Date()}
      />
    </div>
  );
}
